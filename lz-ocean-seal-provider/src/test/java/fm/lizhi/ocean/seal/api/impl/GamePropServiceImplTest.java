package fm.lizhi.ocean.seal.api.impl;

import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean;
import fm.lizhi.ocean.seal.manager.GamePropManager;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 游戏道具服务测试类
 * <AUTHOR>
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
class GamePropServiceImplTest {

    @Mock
    private GamePropManager gamePropManager;

    @InjectMocks
    private GamePropServiceImpl gamePropService;

    private GrantGamePropParam grantParam;
    private GetGamePropListParam listParam;
    private GetGamePropFlowListParam flowListParam;

    @BeforeEach
    void setUp() {
        log.info("Setting up test data");
        
        // 初始化测试参数
        grantParam = GrantGamePropParam.newBuilder()
            .setUserId(12345L)
            .setPropId(1L)
            .setNum(1)
            .setUniqueId("test-unique-id-001")
            .setAppId("test-app-id")
            .setGameId("test-game-id")
            .setChannel("sud")
            .setRemark("test grant")
            .build();

        listParam = GetGamePropListParam.newBuilder()
            .setChannelGameId(1L)
            .setChannelId(1L)
            .setType(1)
            .setPageNumber(1)
            .setPageSize(10)
            .build();

        flowListParam = GetGamePropFlowListParam.newBuilder()
            .setUserId(12345L)
            .setPropId(1L)
            .setGrantStatus(2)
            .setStartTime(System.currentTimeMillis() - 86400000) // 1天前
            .setEndTime(System.currentTimeMillis())
            .setPageNumber(1)
            .setPageSize(10)
            .build();


    }

    @Test
    void testGrantGameProp_Success() {
        log.info("Testing grant game prop success");
        
        // 准备测试数据
        Long expectedFlowId = 123L;
        when(gamePropManager.grantGameProp(any(GrantGamePropParam.class))).thenReturn(expectedFlowId);

        // 执行测试
        Result<ResponseGrantGameProp> result = gamePropService.grantGameProp(grantParam);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getRcode()); // SUCCESS
        assertNotNull(result.getResult());
        assertEquals(2, result.getResult().getGrantStatus()); // 成功状态
        assertEquals(expectedFlowId.longValue(), result.getResult().getFlowId());
        assertEquals("Grant success", result.getResult().getMessage());

        // 验证方法调用
        verify(gamePropManager, times(1)).grantGameProp(grantParam);
    }

    @Test
    void testGrantGameProp_ParamError() {
        log.info("Testing grant game prop param error");
        
        // 测试空参数
        Result<ResponseGrantGameProp> result = gamePropService.grantGameProp(null);

        assertNotNull(result);
        assertEquals(1, result.getRcode()); // PARAM_ERROR
        assertNull(result.getResult());

        // 验证没有调用manager
        verify(gamePropManager, never()).grantGameProp(any());
    }

    @Test
    void testGetGamePropList_Success() {
        log.info("Testing get game prop list success");
        
        // 准备测试数据
        List<GamePropBean> propList = Arrays.asList(createTestGamePropBean());
        int total = 1;

        when(gamePropManager.getGamePropList(any(GetGamePropListParam.class))).thenReturn(propList);
        when(gamePropManager.countGameProp(any(GetGamePropListParam.class))).thenReturn(total);

        // 执行测试
        Result<ResponseGetGamePropList> result = gamePropService.getGamePropList(listParam);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getRcode()); // SUCCESS
        assertNotNull(result.getResult());
        assertEquals(total, result.getResult().getTotal());
        assertEquals(1, result.getResult().getGamePropsCount());

        verify(gamePropManager, times(1)).getGamePropList(listParam);
        verify(gamePropManager, times(1)).countGameProp(listParam);
    }



    // 辅助方法：创建测试用的GamePropBean
    private GamePropBean createTestGamePropBean() {
        GamePropBean bean = new GamePropBean();
        bean.setId(1L);
        bean.setChannelGameId(1L);
        bean.setChannelPropId(1001L);
        bean.setAppId(100L);
        bean.setChannelId(1L);
        bean.setName("测试道具");
        bean.setPropDesc("测试道具描述");
        bean.setType(1);
        bean.setDurationSec(3600);
        bean.setTimeliness(false);
        bean.setIconUrl("http://test.com/icon.png");
        bean.setRemark("测试备注");
        bean.setDeleted(false);
        bean.setOperator("test");
        bean.setCreateTime(new Date());
        bean.setModifyTime(new Date());
        return bean;
    }
}
