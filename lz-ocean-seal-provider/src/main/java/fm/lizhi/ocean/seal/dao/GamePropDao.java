package fm.lizhi.ocean.seal.dao;

import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.dao.mapper.GamePropBeanMapper;
import fm.lizhi.ocean.seal.dao.mapper.ext.GamePropExtMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 游戏道具数据访问对象
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class GamePropDao {

    @Inject
    private GamePropBeanMapper gamePropBeanMapper;

    @Inject
    private GamePropExtMapper gamePropExtMapper;

    /**
     * 根据ID查询道具
     * @param id 道具ID
     * @return 道具信息
     */
    public GamePropBean selectByPrimaryKey(Long id) {
        log.debug("Select game prop by id: {}", id);
        return gamePropBeanMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据渠道游戏ID和渠道道具ID查询道具
     * @param channelGameId 渠道游戏ID
     * @param channelPropId 渠道道具ID
     * @return 道具信息
     */
    public GamePropBean selectByChannelGameIdAndChannelPropId(Long channelGameId, Long channelPropId) {
        log.debug("Select game prop by channelGameId: {}, channelPropId: {}", channelGameId, channelPropId);
        return gamePropExtMapper.selectByChannelGameIdAndChannelPropId(channelGameId, channelPropId);
    }

    /**
     * 根据条件查询道具列表
     * @param channelGameId 渠道游戏ID
     * @param channelId 渠道ID
     * @param type 道具类型
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 道具列表
     */
    public List<GamePropBean> selectByConditions(Long channelGameId, Long channelId, Integer type, 
                                                Integer offset, Integer limit) {
        log.debug("Select game props by conditions: channelGameId={}, channelId={}, type={}, offset={}, limit={}", 
                 channelGameId, channelId, type, offset, limit);
        return gamePropExtMapper.selectByConditions(channelGameId, channelId, type, offset, limit);
    }

    /**
     * 根据条件统计道具数量
     * @param channelGameId 渠道游戏ID
     * @param channelId 渠道ID
     * @param type 道具类型
     * @return 道具数量
     */
    public int countByConditions(Long channelGameId, Long channelId, Integer type) {
        log.debug("Count game props by conditions: channelGameId={}, channelId={}, type={}", 
                 channelGameId, channelId, type);
        return gamePropExtMapper.countByConditions(channelGameId, channelId, type);
    }

    /**
     * 插入道具
     * @param record 道具信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int insert(GamePropBean record) {
        log.info("Insert game prop: {}", record.getName());
        record.setCreateTime(new Date());
        record.setModifyTime(new Date());
        return gamePropBeanMapper.insert(record);
    }

    /**
     * 选择性插入道具
     * @param record 道具信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(GamePropBean record) {
        log.info("Insert selective game prop: {}", record.getName());
        if (record.getCreateTime() == null) {
            record.setCreateTime(new Date());
        }
        if (record.getModifyTime() == null) {
            record.setModifyTime(new Date());
        }
        return gamePropBeanMapper.insertSelective(record);
    }

    /**
     * 根据ID更新道具
     * @param record 道具信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKey(GamePropBean record) {
        log.info("Update game prop by id: {}", record.getId());
        record.setModifyTime(new Date());
        return gamePropBeanMapper.updateByPrimaryKey(record);
    }

    /**
     * 根据ID选择性更新道具
     * @param record 道具信息
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(GamePropBean record) {
        log.info("Update selective game prop by id: {}", record.getId());
        record.setModifyTime(new Date());
        return gamePropBeanMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据ID删除道具（逻辑删除）
     * @param id 道具ID
     * @param operator 操作人
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteByPrimaryKey(Long id, String operator) {
        log.info("Delete game prop by id: {}, operator: {}", id, operator);
        GamePropBean record = new GamePropBean();
        record.setId(id);
        record.setDeleted(true);
        record.setOperator(operator);
        record.setModifyTime(new Date());
        return gamePropBeanMapper.updateByPrimaryKeySelective(record);
    }
}
