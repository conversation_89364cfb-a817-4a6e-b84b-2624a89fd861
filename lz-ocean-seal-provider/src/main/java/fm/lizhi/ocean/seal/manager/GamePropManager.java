package fm.lizhi.ocean.seal.manager;

import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean;
import fm.lizhi.ocean.seal.dao.GamePropDao;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GrantGamePropParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropListParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.GetGamePropFlowListParam;
import fm.lizhi.ocean.seal.protocol.GamePropServiceProto.AddGamePropFlowParam;
import fm.lizhi.ocean.seal.constant.GamePropEventType;
import fm.lizhi.ocean.seal.strategy.GamePropGrantStrategyFactory;
import fm.lizhi.ocean.seal.strategy.GamePropGrantStrategy;
import fm.lizhi.ocean.seal.strategy.GamePropGrantResult;
import fm.lizhi.ocean.seal.dao.GameBizGameDao;
import fm.lizhi.ocean.seal.dao.GameInfoDao;
import fm.lizhi.ocean.seal.dao.GameChannelDao;
import lombok.extern.slf4j.Slf4j;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 游戏道具业务管理类
 * <AUTHOR>
 */
@Slf4j
@AutoBindSingleton
public class GamePropManager {

    @Inject
    private GamePropDao gamePropDao;

    @Inject
    private GamePropGrantStrategyFactory gamePropGrantStrategyFactory;

    @Inject
    private GameBizGameDao gameBizGameDao;

    @Inject
    private GameInfoDao gameInfoDao;

    @Inject
    private GameChannelDao gameChannelDao;

    /**
     * 道具发放
     * @param param 发放参数
     * @return 流水ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long grantGameProp(GrantGamePropParam param) {
        log.info("Grant game prop, param: {}", param);

        // 1. 参数校验
        if (param == null || param.getUserId() <= 0 || param.getPropId() <= 0 ||
            param.getNum() <= 0 || StringUtils.isEmpty(param.getUniqueId())) {
            throw new IllegalArgumentException("Invalid parameters");
        }

        // 2. 幂等性检查
        GamePropFlowBean existingFlow = gamePropDao.selectFlowByUniqueId(param.getUniqueId());
        if (existingFlow != null) {
            log.info("Duplicate grant request, uniqueId: {}, flowId: {}",
                       param.getUniqueId(), existingFlow.getId());
            return existingFlow.getId();
        }

        // 3. 查询道具信息
        GamePropBean gameProp = gamePropDao.selectByPrimaryKey(param.getPropId());
        if (gameProp == null || gameProp.getDeleted()) {
            throw new IllegalArgumentException("Game prop not exists or deleted, propId: " + param.getPropId());
        }

        // 4. 创建流水记录（状态为发放中）
        GamePropFlowBean flowBean = createPropFlowBean(param, gameProp);
        // 1-发放中
        flowBean.setGrantStatus(1);
        gamePropDao.insertFlowSelective(flowBean);
        Long flowId = flowBean.getId();

        try {
            // 5. 调用游戏厂商发放接口
            GamePropGrantResult grantResult = invokeGameVendorGrantProp(param, gameProp);

            // 6. 根据调用结果更新流水状态
            if (grantResult.isSuccess()) {
                // 2-成功
                gamePropDao.updateFlowGrantStatus(flowId, 2);
                log.info("Grant game prop success, flowId: {}, response: {}", flowId, response);
            } else {
                // 3-失败
                gamePropDao.updateFlowGrantStatus(flowId, 3);
                log.warn("Grant game prop failed, flowId: {}, response: {}", flowId, response);
                throw new RuntimeException("Game vendor grant failed: " + response.getMsg());
            }

        } catch (Exception e) {
            // 7. 异常时更新流水状态为失败
            // 3-失败
            gamePropDao.updateFlowGrantStatus(flowId, 3);
            log.error("Grant game prop error, flowId: {}", flowId, e);
            throw e;
        }

        return flowId;
    }

    /**
     * 查询道具列表
     * @param param 查询参数
     * @return 道具列表
     */
    public List<GamePropBean> getGamePropList(GetGamePropListParam param) {
        log.info("Get game prop list, param: {}", param);

        int offset = 0;
        int limit = 20;
        if (param.getPageNumber() > 0 && param.getPageSize() > 0) {
            offset = (param.getPageNumber() - 1) * param.getPageSize();
            limit = param.getPageSize();
        }

        return gamePropDao.selectByConditions(
            param.getChannelGameId(),
            param.getChannelId(),
            param.getType(),
            offset,
            limit
        );
    }

    /**
     * 统计道具数量
     * @param param 查询参数
     * @return 道具数量
     */
    public int countGameProp(GetGamePropListParam param) {
        return gamePropDao.countByConditions(
            param.getChannelGameId(),
            param.getChannelId(),
            param.getType()
        );
    }

    /**
     * 查询道具流水列表
     * @param param 查询参数
     * @return 流水列表
     */
    public List<GamePropFlowBean> getGamePropFlowList(GetGamePropFlowListParam param) {
        log.info("Get game prop flow list, param: {}", param);

        int offset = 0;
        int limit = 20;
        if (param.getPageNumber() > 0 && param.getPageSize() > 0) {
            offset = (param.getPageNumber() - 1) * param.getPageSize();
            limit = param.getPageSize();
        }

        Date startTime = param.getStartTime() > 0 ? new Date(param.getStartTime()) : null;
        Date endTime = param.getEndTime() > 0 ? new Date(param.getEndTime()) : null;

        return gamePropDao.selectFlowByConditions(
            param.getUserId(),
            param.getPropId(),
            param.getGrantStatus(),
            startTime,
            endTime,
            offset,
            limit
        );
    }

    /**
     * 统计道具流水数量
     * @param param 查询参数
     * @return 流水数量
     */
    public int countGamePropFlow(GetGamePropFlowListParam param) {
        Date startTime = param.getStartTime() > 0 ? new Date(param.getStartTime()) : null;
        Date endTime = param.getEndTime() > 0 ? new Date(param.getEndTime()) : null;

        return gamePropDao.countFlowByConditions(
            param.getUserId(),
            param.getPropId(),
            param.getGrantStatus(),
            startTime,
            endTime
        );
    }



    /**
     * 创建道具流水Bean
     */
    private GamePropFlowBean createPropFlowBean(GrantGamePropParam param, GamePropBean gameProp) {
        GamePropFlowBean flowBean = new GamePropFlowBean();
        flowBean.setPropId(param.getPropId());
        flowBean.setUserId(param.getUserId());
        flowBean.setUniqueId(param.getUniqueId());
        flowBean.setDurationSec(gameProp.getDurationSec());
        flowBean.setChannelGameId(gameProp.getChannelGameId());
        flowBean.setChannelPropId(gameProp.getChannelPropId());
        flowBean.setNum(param.getNum());
        flowBean.setAppId(gameProp.getAppId());
        flowBean.setType(gameProp.getType());
        flowBean.setCreateTime(new Date());
        flowBean.setModifyTime(new Date());
        return flowBean;
    }

    /**
     * 调用游戏厂商道具发放接口
     */
    private ResponseInvokeTarget invokeGameVendorGrantProp(GrantGamePropParam param, GamePropBean gameProp) {
        // 构建调用参数
        JSONObject dataJson = new JSONObject();
        dataJson.put("userId", param.getUserId());
        dataJson.put("propId", gameProp.getChannelPropId());
        dataJson.put("num", param.getNum());
        dataJson.put("uniqueId", param.getUniqueId());
        dataJson.put("durationSec", gameProp.getDurationSec());
        dataJson.put("remark", param.getRemark());

        InvokeTargetParams invokeParam = InvokeTargetParams.newBuilder()
            .setEventName(GamePropEventType.GRANT_PROP.getEvent()) // 道具发放事件
            .setChannel(param.getChannel())
            .setAppId(param.getAppId())
            .setGameId(param.getGameId())
            .setDataJson(dataJson.toJSONString())
            .build();

        return gameProxyManager.invokeTarget(invokeParam);
    }

    /**
     * 判断发放是否成功
     */
    private boolean isGrantSuccess(ResponseInvokeTarget response) {
        return response != null && response.getBizCode() == 0;
    }
}
