package fm.lizhi.ocean.seal.dao.mapper;

import fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 游戏道具流水数据访问接口
 * <AUTHOR>
 */
@Mapper
public interface GamePropFlowMapper {

    /**
     * 根据ID查询流水
     * @param id 流水ID
     * @return 流水信息
     */
    GamePropFlowBean selectByPrimaryKey(Long id);

    /**
     * 根据唯一ID查询流水（用于幂等性检查）
     * @param uniqueId 唯一ID
     * @return 流水信息
     */
    GamePropFlowBean selectByUniqueId(@Param("uniqueId") String uniqueId);

    /**
     * 根据条件查询流水列表
     * @param userId 用户ID
     * @param propId 道具ID
     * @param grantStatus 发放状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 流水列表
     */
    List<GamePropFlowBean> selectByConditions(@Param("userId") Long userId,
                                             @Param("propId") Long propId,
                                             @Param("grantStatus") Integer grantStatus,
                                             @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime,
                                             @Param("offset") Integer offset,
                                             @Param("limit") Integer limit);

    /**
     * 根据条件统计流水数量
     * @param userId 用户ID
     * @param propId 道具ID
     * @param grantStatus 发放状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 流水数量
     */
    int countByConditions(@Param("userId") Long userId,
                         @Param("propId") Long propId,
                         @Param("grantStatus") Integer grantStatus,
                         @Param("startTime") Date startTime,
                         @Param("endTime") Date endTime);

    /**
     * 插入流水
     * @param record 流水信息
     * @return 影响行数
     */
    int insert(GamePropFlowBean record);

    /**
     * 选择性插入流水
     * @param record 流水信息
     * @return 影响行数
     */
    int insertSelective(GamePropFlowBean record);

    /**
     * 根据ID更新流水
     * @param record 流水信息
     * @return 影响行数
     */
    int updateByPrimaryKey(GamePropFlowBean record);

    /**
     * 根据ID选择性更新流水
     * @param record 流水信息
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(GamePropFlowBean record);

    /**
     * 更新流水状态
     * @param id 流水ID
     * @param grantStatus 发放状态
     * @return 影响行数
     */
    int updateGrantStatus(@Param("id") Long id, @Param("grantStatus") Integer grantStatus);

    /**
     * 根据用户ID和道具ID查询用户道具流水
     * @param userId 用户ID
     * @param propId 道具ID
     * @param grantStatus 发放状态（可选）
     * @return 流水列表
     */
    List<GamePropFlowBean> selectByUserIdAndPropId(@Param("userId") Long userId,
                                                   @Param("propId") Long propId,
                                                   @Param("grantStatus") Integer grantStatus);
}
