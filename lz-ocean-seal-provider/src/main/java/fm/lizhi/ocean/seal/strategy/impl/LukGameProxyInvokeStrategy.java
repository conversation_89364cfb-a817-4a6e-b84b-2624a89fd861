package fm.lizhi.ocean.seal.strategy.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.inject.Inject;
import com.netflix.governator.annotations.AutoBindSingleton;
import fm.lizhi.commons.config.util.JsonUtil;
import fm.lizhi.ocean.seal.constant.GameChannel;
import fm.lizhi.ocean.seal.constant.GameOperateEventMapping;
import fm.lizhi.ocean.seal.constant.GamePropEventMapping;
import fm.lizhi.ocean.seal.dao.bean.GameBizGameBean;
import fm.lizhi.ocean.seal.dao.bean.GameChannelBean;
import fm.lizhi.ocean.seal.dao.bean.GameInfoBean;
import fm.lizhi.ocean.seal.manager.LukManger;
import fm.lizhi.ocean.seal.protocol.GameProxyServiceProto;
import fm.lizhi.ocean.seal.strategy.GameProxyInvokeStrategy;
import io.github.cfgametech.Response;
import io.github.cfgametech.SDK;
import io.github.cfgametech.beans.PublishControlEventRequest;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;

import static fm.lizhi.ocean.seal.api.GameProxyService.INVOKE_TARGET_ERROR;

/**
 * LUK 渠道游戏代理调用策略实现
 * 使用 LUK SDK 方式调用 LUK 渠道接口
 * 
 * Created in 2025-07-01
 *
 */
@Slf4j
@AutoBindSingleton
public class LukGameProxyInvokeStrategy implements GameProxyInvokeStrategy {

    @Inject
    private LukManger lukManager;
    
    @Override
    public GameProxyServiceProto.ResponseInvokeTarget invokeTarget(
            GameProxyServiceProto.InvokeTargetParams param,
            GameBizGameBean bizGameBean,
            GameInfoBean gameInfoBean, 
            GameChannelBean gameChannelBean) {
        
        log.info("LUK strategy invoke target, gameId: {}, channelGameId: {}, event: {}", 
                bizGameBean.getId(), gameInfoBean.getChannelGameIdStr(), param.getEventName());
        
        try {
            // 创建 LUK SDK 实例
            SDK lukSdk = lukManager.createLukSdk(bizGameBean, gameChannelBean, param.getAppId());
            if (lukSdk == null) {
                throw new RuntimeException("Failed to create LUK SDK");
            }
            
            // 根据事件类型调用不同的 SDK 方法
            Response<?> sdkResult = invokeLukSdkMethod(lukSdk, param, gameInfoBean, gameChannelBean);
            // 将 SDK 结果转换为统一的响应格式
            return convertLukResultToResponse(sdkResult);
            
        } catch (Exception e) {
            log.error("LUK strategy invoke target failed, gameId: {}, event: {}", 
                     bizGameBean.getId(), param.getEventName(), e);
            throw new RuntimeException("LUK SDK invoke failed", e);
        }
    }

    
    /**
     * 根据事件类型调用相应的 LUK SDK 方法
     */
    private Response<?> invokeLukSdkMethod(SDK lukSdk,
                                     GameProxyServiceProto.InvokeTargetParams param,
                                     GameInfoBean gameInfoBean,
                                     GameChannelBean gameChannelBean) throws IOException, IllegalAccessException {
        
        String eventName = param.getEventName();
        Map<String, Object> dataMap = JSONObject.parseObject(param.getDataJson(), Map.class);
        
        log.info("LUK SDK invoke method, event: {}, gameId: {}, data: {}", eventName, gameInfoBean.getChannelGameIdStr(), dataMap);
        
        // 根据事件映射获取 LUK 渠道的事件类型
        GameOperateEventMapping eventMapping = findLukEventMapping(eventName);
        if (eventMapping == null) {
            log.error("Failed to find LUK event mapping for event: {}", eventName);
            throw new IllegalAccessException("Failed to find LUK event mapping for event: " + eventName);
        }

        PublishControlEventRequest.Builder builder = new PublishControlEventRequest.Builder();
        builder.setAppId(Integer.parseInt(gameChannelBean.getAppId()));
        builder.setGameId(Math.toIntExact(gameInfoBean.getChannelGameId()));
        builder.setRoomId(StrUtil.isNotBlank(param.getRoomId()) ? param.getRoomId() : MapUtil.getStr(dataMap, "room_id"));
        builder.setTimestamp(System.currentTimeMillis());
        builder.setType((Integer) eventMapping.getChannelEvent());
        builder.setData(JSONObject.toJSONString(dataMap));
        return lukSdk.PublishControlEvent(builder.build());

    }

    /**
     * 查找 LUK 事件映射
     */
    private GameOperateEventMapping findLukEventMapping(String eventName) {
        for (GameOperateEventMapping mapping : GameOperateEventMapping.values()) {
            if (GameChannel.LUK.equals(mapping.getChannel()) &&
                eventName.equals(mapping.getSealEvent())) {
                return mapping;
            }
        }
        return null;
    }
    

    /**
     * 将 LUK SDK 的返回结果转换为标准的 ResponseInvokeTarget 格式
     */
    private GameProxyServiceProto.ResponseInvokeTarget convertLukResultToResponse(Response<?> sdkResult) {
        GameProxyServiceProto.ResponseInvokeTarget.Builder builder = 
                GameProxyServiceProto.ResponseInvokeTarget.newBuilder();
        
        try {
            if (sdkResult != null) {
                // 处理标准的 LUK SDK Response 格式
                if (sdkResult.suc()) {
                    builder.setBizCode(0);
                    builder.setMsg("success");
                    builder.setData(JsonUtil.dumps(sdkResult.getData()));
                } else {
                    builder.setBizCode(sdkResult.getCode());
                    builder.setMsg(sdkResult.getMessage());
                    builder.setData("");
                }
            }
            
        } catch (Exception e) {
            log.error("Error converting LUK result to response", e);
            builder.setBizCode(INVOKE_TARGET_ERROR);
            builder.setMsg("Failed to convert LUK response: " + e.getMessage());
            builder.setData("");
        }
        
        return builder.build();
    }
    
    @Override
    public boolean supports(String channel) {
        return GameChannel.LUK.equals(channel);
    }
}
