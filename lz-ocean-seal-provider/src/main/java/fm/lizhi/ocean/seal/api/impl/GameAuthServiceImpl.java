package fm.lizhi.ocean.seal.api.impl;

import com.google.common.base.Strings;
import com.google.inject.Inject;
import fm.lizhi.commons.service.client.annotation.ServiceProvider;
import fm.lizhi.commons.service.client.log.LogContext;
import fm.lizhi.commons.service.client.pojo.Result;
import fm.lizhi.commons.service.client.rcode.GeneralRCode;
import fm.lizhi.game.auth.SealAuth;
import fm.lizhi.game.auth.enums.SealAuthResultCode;
import fm.lizhi.game.auth.pojo.JwtUserInfo;
import fm.lizhi.game.auth.pojo.ParseResult;
import fm.lizhi.game.auth.pojo.SealAuthCode;
import fm.lizhi.game.auth.pojo.SealAuthToken;
import fm.lizhi.ocean.seal.api.GameAuthService;
import fm.lizhi.ocean.seal.constant.*;
import fm.lizhi.ocean.seal.gamechannel.ChannelSudAuthorService;
import fm.lizhi.ocean.seal.gamechannel.SudAuthorService;
import fm.lizhi.ocean.seal.manager.GameAppManager;
import fm.lizhi.ocean.seal.manager.GameUserManager;
import fm.lizhi.ocean.seal.pojo.bo.ChannelCodeInfo;
import fm.lizhi.ocean.seal.pojo.bo.GameChannelAuthInfo;
import fm.lizhi.ocean.seal.pojo.bo.GameUidInfo;
import fm.lizhi.ocean.seal.protocol.GameAuthServiceProto;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import tech.sud.mgp.auth.ErrorCodeEnum;
import tech.sud.mgp.auth.api.SudCode;
import tech.sud.mgp.auth.api.SudMGPAuth;
import tech.sud.mgp.auth.api.SudSSToken;
import tech.sud.mgp.auth.api.SudUid;

import java.util.Optional;

@Slf4j
@ServiceProvider
public class GameAuthServiceImpl implements GameAuthService {

    @Inject
    private SudAuthorService sudAuthorService;
    @Inject
    private GameUserManager gameUserManager;
    @Inject
    private GameAppManager gameAppManager;
    @Inject
    private ChannelSudAuthorService channelSudAuthorService;

    /**
     * 平台SDK与游戏渠道中游戏使用的Token
     *
     * @param userId  用户ID
     * @param appId   appId
     * @param channel 游戏渠道
     * @return
     */
    @Override
    public Result<GameAuthServiceProto.ResponseGetLoginToken> getLoginToken(long userId, String appId, String channel) {
        String logStr = "userId={}`appId={}`channel={}";
        LogContext.addReqLog(logStr, userId, appId, channel);
        LogContext.addResLog(logStr, userId, appId, channel);
        GameAuthServiceProto.ResponseGetLoginToken.Builder resp = GameAuthServiceProto.ResponseGetLoginToken.newBuilder();
        String uid = gameUserManager.getGameUserId(userId, appId);
        if (Strings.isNullOrEmpty(uid)) {
            return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_ERROR, resp.build());
        }
        try {
            if(GameChannel.LIZHI.equals(channel) || GameChannel.LUK.equals(channel)) {
                GameAuthServiceProto.LoginToken loginToken = getLoginTokenByChannel(uid, appId, channel);
                if(loginToken == null) {
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }
                resp.setToken(loginToken);
            } else if (GameChannel.SUD.equals(channel)){
                ChannelCodeInfo channelCodeInfo = channelSudAuthorService.getCode(appId, uid);
                if (channelCodeInfo == null) {
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }

                resp.setToken(GameAuthServiceProto.LoginToken.newBuilder()
                        .setToken(channelCodeInfo.getCode())
                        .setExpireDate(channelCodeInfo.getExpireTime()).build());
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth.getCode fail, userId:{}, appId:{}, channel:{}, msg:{}", userId, appId, channel, e.getMessage(), e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }


    @Override
    public Result<GameAuthServiceProto.ResponseGetServerToken> getServerToken(String code, String channel, String appId) {
        String logStr = "loginToken={}`channel={}`appId={}";
        LogContext.addReqLog(logStr, code, channel, appId);
        LogContext.addResLog(logStr, code, channel, appId);
        return getServerTokenResult(code, channel, appId, null, null);

    }

    @Override
    public Result<GameAuthServiceProto.ResponseUpdateServerToken> updateServerToken(String token, String channel, String appId) {
        String logStr = "token={}`channel={}`appId={}";
        LogContext.addReqLog(logStr, token, channel, appId);
        LogContext.addResLog(logStr, token, channel, appId);
        GameAuthServiceProto.ResponseUpdateServerToken.Builder resp = GameAuthServiceProto.ResponseUpdateServerToken.newBuilder();
        try {
            if(ChannelType.LIZHI.getName().equals(channel)) {
                SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.LIZHI.getName());
                if(null == sealAuth){
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }

                ParseResult parseResult = sealAuth.getUserInfoInSealAuthToken(token);
                JwtUserInfo userInfo = parseResult.getUserInfo();
                if(!parseResult.isSuccess() || userInfo == null) {
                    log.warn("parse SealCode fail;parseResult={};sealResourceToken={}", parseResult, token);
                    resp.setToken(GameAuthServiceProto.ServerToken.newBuilder().setErrorCode(parseResult.getErrorCode().getCode()).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                SealAuthToken sealAuthToken = sealAuth.getSealAuthToken(userInfo);
                resp.setToken(GameAuthServiceProto.ServerToken.newBuilder()
                        .setToken(sealAuthToken.getToken()).setExpireDate(sealAuthToken.getExpireDate()).build());
            } else {
                GameChannelAuthInfo gameChannelAuthInfo = channelSudAuthorService.getAuthInfo(appId, token, TokenTypeEnum.CHANNEL_SS_TOKEN, null, null);
                if (!gameChannelAuthInfo.isSuccess() && gameChannelAuthInfo.getErrorCode() == PlatformMessageCodeEnum.NOT_FOUND.getCode()) {
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }

                if (!gameChannelAuthInfo.isSuccess()) {
                    resp.setToken(GameAuthServiceProto.ServerToken.newBuilder().setErrorCode(gameChannelAuthInfo.getErrorCode()).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }


                resp.setToken(GameAuthServiceProto.ServerToken.newBuilder()
                        .setToken(gameChannelAuthInfo.getSsToken()).setExpireDate(gameChannelAuthInfo.getExpireTime()).build());
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth updateSSToken fail", e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }

    @Override
    public Result<GameAuthServiceProto.ResponseGetUserByServerToken> getUserByServerToken(String token, String channel, String appId) {
        String logStr = "token={}`channel={}`appId={}";
        LogContext.addReqLog(logStr, token, channel, appId);
        LogContext.addResLog(logStr, token, channel, appId);
        GameAuthServiceProto.ResponseGetUserByServerToken.Builder resp = GameAuthServiceProto.ResponseGetUserByServerToken.newBuilder();
        long userIdByGameUserId;
        try {
            if(ChannelType.LIZHI.getName().equals(channel)) {
                SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.LIZHI.getName());
                if(null == sealAuth){
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }

                ParseResult parseResult = sealAuth.getUserInfoInSealAuthToken(token);
                JwtUserInfo userInfo = parseResult.getUserInfo();
                if(!parseResult.isSuccess() || userInfo == null) {
                    log.warn("parse SealCode fail;parseResult={};sealResourceToken={}", parseResult, token);
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                userIdByGameUserId = userInfo.getId();
            } else {
                PlatformMessageCodeEnum platformMessageCodeEnum = channelSudAuthorService.verifySSToke(appId, token);
                if (platformMessageCodeEnum.getCode() != PlatformMessageCodeEnum.SUCCESS.getCode()) {
                    resp.setGameUser(GameAuthServiceProto.GameUser.newBuilder().setErrorCode(platformMessageCodeEnum.getCode()).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }

                GameUidInfo gameUidInfo = channelSudAuthorService.getUid(token, TokenTypeEnum.CHANNEL_SS_TOKEN);
                if (!gameUidInfo.isSuccess()) {
                    int errorCode = gameUidInfo.getErrorCode();
                    log.warn("updateSSToken fail.errorCode={}`ssToken={}", errorCode, token);
                    resp.setGameUser(GameAuthServiceProto.GameUser.newBuilder().setErrorCode(errorCode).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                userIdByGameUserId = gameUserManager.converUid2SealUserId(gameUidInfo.getUid());
            }
            if (userIdByGameUserId == 0L) {
                return new Result<>(SealRCode.SEAL_RCODE_UNKNOWN_ERROR, resp.build());
            }
            resp.setGameUser(GameAuthServiceProto.GameUser.newBuilder()
                    .setGameUserId(String.valueOf(userIdByGameUserId)).setUserId(userIdByGameUserId).setAppId(appId).build());
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth getUidBySSToken fail", e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }

    @Override
    public Result<GameAuthServiceProto.ResponseVerifyLoginToken> verifyLoginToken(String token, String channel, String appId) {
        String logStr = "token={}`channel={}`appId={}";
        LogContext.addReqLog(logStr, token, channel, appId);
        LogContext.addResLog(logStr, token, channel, appId);
        GameAuthServiceProto.ResponseVerifyLoginToken.Builder resp = GameAuthServiceProto.ResponseVerifyLoginToken.newBuilder();
        try {
            if(ChannelType.LIZHI.getName().equals(channel)) {
                SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.LIZHI.getName());
                if(null == sealAuth){
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }

                SealAuthResultCode resultCode = sealAuth.verifySealAuthCode(token);
                resp.setErrorCode(resultCode.getCode());
            } else {
                PlatformMessageCodeEnum platformMessageCodeEnum = channelSudAuthorService.verifyCode(appId, token);
                resp.setErrorCode(platformMessageCodeEnum.getCode());
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth getUidBySSToken fail", e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }

    @Override
    public Result<GameAuthServiceProto.ResponseVerifyServerToken> verifyServerToken(String token, String channel, String appId) {
        String logStr = "token={}`channel={}`appId={}";
        LogContext.addReqLog(logStr, token, channel, appId);
        LogContext.addResLog(logStr, token, channel, appId);
        GameAuthServiceProto.ResponseVerifyServerToken.Builder resp = GameAuthServiceProto.ResponseVerifyServerToken.newBuilder();
        try {
            if(ChannelType.LIZHI.getName().equals(channel)) {
                SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.LIZHI.getName());
                if(null == sealAuth){
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }

                SealAuthResultCode resultCode = sealAuth.verifySealAuthCode(token);
                resp.setErrorCode(resultCode.getCode());
            } else {
                PlatformMessageCodeEnum platformMessageCodeEnum = channelSudAuthorService.verifySSToke(appId, token);
                if (PlatformMessageCodeEnum.NOT_FOUND == platformMessageCodeEnum) {
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }
                resp.setErrorCode(platformMessageCodeEnum.getCode());
            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth getUidBySSToken fail", e);
        }
        return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
    }

    @Override
    public Result<GameAuthServiceProto.ResponseGetChannelServerToken> getChannelServerToken(GameAuthServiceProto.ChannelTokenParam channelTokenParam) {
        String logStr = "loginToken={}`channel={}`appId={}`timestamp={}`sign={}";
        LogContext.addReqLog(logStr, channelTokenParam.getLoginToken(), channelTokenParam.getChannel(), channelTokenParam.getAppId(), channelTokenParam.getTimestamp(), channelTokenParam.getSign());
        LogContext.addResLog(logStr, channelTokenParam.getLoginToken(), channelTokenParam.getChannel(), channelTokenParam.getAppId(), channelTokenParam.getTimestamp(), channelTokenParam.getSign());
        getServerTokenResult(channelTokenParam.getLoginToken(), channelTokenParam.getChannel(), channelTokenParam.getAppId(), channelTokenParam.getTimestamp(), channelTokenParam.getSign());
        return null;
    }

    private @NotNull Result<GameAuthServiceProto.ResponseGetServerToken> getServerTokenResult(String code, String channel, String appId, Integer timestamp, String sign) {
        GameAuthServiceProto.ResponseGetServerToken.Builder resp = GameAuthServiceProto.ResponseGetServerToken.newBuilder();
        try {
            if(ChannelType.LIZHI.getName().equals(channel)) {
                SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, ChannelType.LIZHI.getName());
                if(null == sealAuth){
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }
                ParseResult parseResult = sealAuth.getUserInfoInSealAuthCode(code);
                JwtUserInfo userInfo = parseResult.getUserInfo();
                if(!parseResult.isSuccess() || userInfo == null) {
                    log.warn("parse SealCode fail;parseResult={};loginToken={}", parseResult, code);
                    resp.setToken(GameAuthServiceProto.ServerToken.newBuilder().setErrorCode(parseResult.getErrorCode().getCode()).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }
                SealAuthToken sealAuthToken = sealAuth.getSealAuthToken(userInfo);
                resp.setToken(GameAuthServiceProto.ServerToken.newBuilder()
                        .setToken(sealAuthToken.getToken()).setExpireDate(sealAuthToken.getExpireDate()).build());
            } else if (ChannelType.SUD.getName().equals(channel)) {
                GameChannelAuthInfo gameChannelAuthInfo = channelSudAuthorService.getAuthInfo(appId, code, TokenTypeEnum.CHANNEL_CODE, null, null);
                if (gameChannelAuthInfo.getErrorCode() == PlatformMessageCodeEnum.NOT_FOUND.getCode()) {
                    return new Result<>(SealRCode.SEAL_RCODE_GAME_AUTH_SDK_ERROR, resp.build());
                }

                if (!gameChannelAuthInfo.isSuccess()) {
                    resp.setToken(GameAuthServiceProto.ServerToken.newBuilder().setErrorCode(gameChannelAuthInfo.getErrorCode()).build());
                    return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
                }

                resp.setToken(GameAuthServiceProto.ServerToken.newBuilder()
                        .setToken(gameChannelAuthInfo.getSsToken())
                        .setExpireDate(gameChannelAuthInfo.getExpireTime())
                        .setUid(gameChannelAuthInfo.getUid())
                        .build());
            } else if (ChannelType.LUK.getName().equals(channel)) {

            }
            return new Result<>(GeneralRCode.GENERAL_RCODE_SUCCESS, resp.build());
        } catch (Exception e) {
            log.error("sudMGPAuth getSSToken fail;code={};channel={}", code, channel, e);
            return new Result<>(SealRCode.SEAL_RCODE_GAME_SDK_UNKNOWN_ERROR, resp.build());
        }
    }

    /**
     * 生成渠道 Code
     */
    private GameAuthServiceProto.LoginToken getLoginTokenByChannel(String uid, String appId, String channel) {
        if (GameChannel.LIZHI.equals(channel) || GameChannel.LUK.equals(channel)) {
            SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, channel);
            if(null == sealAuth){
                return null;
            }
            JwtUserInfo jwtUserInfo = new JwtUserInfo();
            jwtUserInfo.setId(Long.parseLong(uid));
            SealAuthCode sealAuthCode = sealAuth.getSealAuthCode(jwtUserInfo);
            return GameAuthServiceProto.LoginToken.newBuilder()
                    .setToken(sealAuthCode.getCode())
                    .setExpireDate(sealAuthCode.getExpireDate()).build();
        }
        return null;
    }


    /**
     * 根据Code获取用户信息
     * todo 需要验证一下能不能正常解析出来
     * @param appId
     * @param channel
     * @param code
     * @return
     */
    private Optional<JwtUserInfo> getUserInfoByCode(String appId, String channel, String code){

        SealAuth sealAuth = gameAppManager.createLiZhiSealAuth(appId, channel);
        if(null == sealAuth){
            return Optional.empty();
        }

        ParseResult parseResult = sealAuth.getUserInfoInSealAuthCode(code);
        JwtUserInfo userInfo = parseResult.getUserInfo();
        if(!parseResult.isSuccess() || userInfo == null) {
            log.warn("parse SealCode fail;parseResult={};loginToken={}", parseResult, code);
            return Optional.empty();
        }
        return Optional.of(userInfo);
    }


}
