package fm.lizhi.ocean.seal.dao.mapper;

import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 游戏道具数据访问接口
 * <AUTHOR>
 */
@Mapper
public interface GamePropMapper {

    /**
     * 根据ID查询道具
     * @param id 道具ID
     * @return 道具信息
     */
    GamePropBean selectByPrimaryKey(Long id);

    /**
     * 根据渠道游戏ID和渠道道具ID查询道具
     * @param channelGameId 渠道游戏ID
     * @param channelPropId 渠道道具ID
     * @return 道具信息
     */
    GamePropBean selectByChannelGameIdAndChannelPropId(@Param("channelGameId") Long channelGameId, 
                                                       @Param("channelPropId") Long channelPropId);

    /**
     * 根据条件查询道具列表
     * @param channelGameId 渠道游戏ID
     * @param channelId 渠道ID
     * @param type 道具类型
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 道具列表
     */
    List<GamePropBean> selectByConditions(@Param("channelGameId") Long channelGameId,
                                         @Param("channelId") Long channelId,
                                         @Param("type") Integer type,
                                         @Param("offset") Integer offset,
                                         @Param("limit") Integer limit);

    /**
     * 根据条件统计道具数量
     * @param channelGameId 渠道游戏ID
     * @param channelId 渠道ID
     * @param type 道具类型
     * @return 道具数量
     */
    int countByConditions(@Param("channelGameId") Long channelGameId,
                         @Param("channelId") Long channelId,
                         @Param("type") Integer type);

    /**
     * 插入道具
     * @param record 道具信息
     * @return 影响行数
     */
    int insert(GamePropBean record);

    /**
     * 选择性插入道具
     * @param record 道具信息
     * @return 影响行数
     */
    int insertSelective(GamePropBean record);

    /**
     * 根据ID更新道具
     * @param record 道具信息
     * @return 影响行数
     */
    int updateByPrimaryKey(GamePropBean record);

    /**
     * 根据ID选择性更新道具
     * @param record 道具信息
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(GamePropBean record);

    /**
     * 根据ID删除道具（逻辑删除）
     * @param id 道具ID
     * @param operator 操作人
     * @return 影响行数
     */
    int deleteByPrimaryKey(@Param("id") Long id, @Param("operator") String operator);
}
