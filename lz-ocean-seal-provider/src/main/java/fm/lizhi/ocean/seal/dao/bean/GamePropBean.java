package fm.lizhi.ocean.seal.dao.bean;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * This class was generated by DataStore MyBatis Generator.
 *
 * 游戏道具表，现阶段将装扮和道具，都归类为游戏道具
 *
 * @date 2025-07-01 06:42:34
 */
@Table(name = "`game_prop`")
@Getter
@Setter
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GamePropBean {
    @Id
    @GeneratedValue
    @Column(name= "`id`")
    private Long id;

    /**
     * 渠道游戏ID
     */
    @Column(name= "`channel_game_id`")
    private Long channelGameId;

    /**
     * 渠道道具 ID
     */
    @Column(name= "`channel_prop_id`")
    private Long channelPropId;

    /**
     * seal 分发给业务的 ID
     */
    @Column(name= "`app_id`")
    private Long appId;

    /**
     * 渠道ID
     */
    @Column(name= "`channel_id`")
    private Long channelId;

    /**
     * 道具名称
     */
    @Column(name= "`name`")
    private String name;

    /**
     * 道具描述
     */
    @Column(name= "`prop_desc`")
    private String propDesc;

    /**
     * 道具类型, 1皮肤, 2道具
     */
    @Column(name= "`type`")
    private Integer type;

    /**
     * 有效时长（单位：秒），小于 0 为永久
     */
    @Column(name= "`duration_sec`")
    private Integer durationSec;

    /**
     * 是否是时效性道具，如果是，可能会忽略duration_sec
     */
    @Column(name= "`timeliness`")
    private Boolean timeliness;

    /**
     * 参考图片
     */
    @Column(name= "`icon_url`")
    private String iconUrl;

    /**
     * 备注
     */
    @Column(name= "`remark`")
    private String remark;

    /**
     * 是否删除 0未删除, 1已删除
     */
    @Column(name= "`deleted`")
    private Boolean deleted;

    /**
     * 操作人
     */
    @Column(name= "`operator`")
    private String operator;

    /**
     * 创建时间
     */
    @Column(name= "`create_time`")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name= "`modify_time`")
    private Date modifyTime;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", channelGameId=").append(channelGameId);
        sb.append(", channelPropId=").append(channelPropId);
        sb.append(", appId=").append(appId);
        sb.append(", channelId=").append(channelId);
        sb.append(", name=").append(name);
        sb.append(", propDesc=").append(propDesc);
        sb.append(", type=").append(type);
        sb.append(", durationSec=").append(durationSec);
        sb.append(", timeliness=").append(timeliness);
        sb.append(", iconUrl=").append(iconUrl);
        sb.append(", remark=").append(remark);
        sb.append(", deleted=").append(deleted);
        sb.append(", operator=").append(operator);
        sb.append(", createTime=").append(createTime);
        sb.append(", modifyTime=").append(modifyTime);
        sb.append("]");
        return sb.toString();
    }
}