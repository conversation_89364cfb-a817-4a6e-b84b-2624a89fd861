package fm.lizhi.ocean.seal.dao.mapper.ext;

import fm.lizhi.common.datastore.core.annotation.DataStore;
import fm.lizhi.ocean.seal.dao.bean.GamePropBean;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 游戏道具扩展数据访问接口
 * <AUTHOR>
 */
@DataStore(namespace = "mysql_ocean_oceanseal")
public interface GamePropExtMapper {

    /**
     * 根据渠道游戏ID和渠道道具ID查询道具
     * @param channelGameId 渠道游戏ID
     * @param channelPropId 渠道道具ID
     * @return 道具信息
     */
    @Select("SELECT * FROM game_prop WHERE channel_game_id = #{channelGameId} AND channel_prop_id = #{channelPropId} AND deleted = 0")
    GamePropBean selectByChannelGameIdAndChannelPropId(@Param("channelGameId") Long channelGameId, 
                                                       @Param("channelPropId") Long channelPropId);

    /**
     * 根据条件查询道具列表
     * @param channelGameId 渠道游戏ID
     * @param channelId 渠道ID
     * @param type 道具类型
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 道具列表
     */
    @Select("<script>" +
            "SELECT * FROM game_prop WHERE deleted = 0 " +
            "<if test='channelGameId != null'> AND channel_game_id = #{channelGameId} </if>" +
            "<if test='channelId != null'> AND channel_id = #{channelId} </if>" +
            "<if test='type != null'> AND type = #{type} </if>" +
            "ORDER BY create_time DESC " +
            "<if test='offset != null and limit != null'> LIMIT #{offset}, #{limit} </if>" +
            "</script>")
    List<GamePropBean> selectByConditions(@Param("channelGameId") Long channelGameId,
                                         @Param("channelId") Long channelId,
                                         @Param("type") Integer type,
                                         @Param("offset") Integer offset,
                                         @Param("limit") Integer limit);

    /**
     * 根据条件统计道具数量
     * @param channelGameId 渠道游戏ID
     * @param channelId 渠道ID
     * @param type 道具类型
     * @return 道具数量
     */
    int countByConditions(@Param("channelGameId") Long channelGameId,
                         @Param("channelId") Long channelId,
                         @Param("type") Integer type);
}
