<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="fm.lizhi.ocean.seal.dao.mapper.ext.GamePropFlowExtMapper">
  
  <resultMap id="BaseResultMap" type="fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="prop_id" jdbcType="BIGINT" property="propId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="unique_id" jdbcType="VARCHAR" property="uniqueId" />
    <result column="duration_sec" jdbcType="INTEGER" property="durationSec" />
    <result column="channel_game_id" jdbcType="BIGINT" property="channelGameId" />
    <result column="channel_prop_id" jdbcType="BIGINT" property="channelPropId" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="app_id" jdbcType="BIGINT" property="appId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="grant_status" jdbcType="INTEGER" property="grantStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="selectByConditions" resultMap="BaseResultMap">
    SELECT * FROM game_prop_flow WHERE 1=1
    <if test="userId != null">
      AND user_id = #{userId,jdbcType=BIGINT}
    </if>
    <if test="propId != null">
      AND prop_id = #{propId,jdbcType=BIGINT}
    </if>
    <if test="grantStatus != null">
      AND grant_status = #{grantStatus,jdbcType=INTEGER}
    </if>
    <if test="startTime != null">
      AND create_time >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
    </if>
    ORDER BY create_time DESC
    <if test="offset != null and limit != null">
      LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </if>
  </select>

  <select id="countByConditions" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM game_prop_flow WHERE 1=1
    <if test="userId != null">
      AND user_id = #{userId,jdbcType=BIGINT}
    </if>
    <if test="propId != null">
      AND prop_id = #{propId,jdbcType=BIGINT}
    </if>
    <if test="grantStatus != null">
      AND grant_status = #{grantStatus,jdbcType=INTEGER}
    </if>
    <if test="startTime != null">
      AND create_time >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      AND create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="selectByUserIdAndPropId" resultMap="BaseResultMap">
    SELECT * FROM game_prop_flow 
    WHERE user_id = #{userId,jdbcType=BIGINT}
      AND prop_id = #{propId,jdbcType=BIGINT}
    <if test="grantStatus != null">
      AND grant_status = #{grantStatus,jdbcType=INTEGER}
    </if>
    ORDER BY create_time DESC
  </select>

</mapper>
