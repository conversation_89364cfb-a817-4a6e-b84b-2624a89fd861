<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="fm.lizhi.ocean.seal.dao.mapper.ext.GamePropExtMapper">
  
  <resultMap id="BaseResultMap" type="fm.lizhi.ocean.seal.dao.bean.GamePropBean">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel_game_id" jdbcType="BIGINT" property="channelGameId" />
    <result column="channel_prop_id" jdbcType="BIGINT" property="channelPropId" />
    <result column="app_id" jdbcType="BIGINT" property="appId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="prop_desc" jdbcType="VARCHAR" property="propDesc" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="duration_sec" jdbcType="INTEGER" property="durationSec" />
    <result column="timeliness" jdbcType="BIT" property="timeliness" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="selectByConditions" resultMap="BaseResultMap">
    SELECT * FROM game_prop WHERE deleted = 0
    <if test="channelGameId != null">
      AND channel_game_id = #{channelGameId,jdbcType=BIGINT}
    </if>
    <if test="channelId != null">
      AND channel_id = #{channelId,jdbcType=BIGINT}
    </if>
    <if test="type != null">
      AND type = #{type,jdbcType=INTEGER}
    </if>
    ORDER BY create_time DESC
    <if test="offset != null and limit != null">
      LIMIT #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </if>
  </select>

  <select id="countByConditions" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM game_prop WHERE deleted = 0
    <if test="channelGameId != null">
      AND channel_game_id = #{channelGameId,jdbcType=BIGINT}
    </if>
    <if test="channelId != null">
      AND channel_id = #{channelId,jdbcType=BIGINT}
    </if>
    <if test="type != null">
      AND type = #{type,jdbcType=INTEGER}
    </if>
  </select>

</mapper>
