<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="fm.lizhi.ocean.seal.dao.mapper.GamePropMapper">
  
  <resultMap id="BaseResultMap" type="fm.lizhi.ocean.seal.dao.bean.GamePropBean">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="channel_game_id" jdbcType="BIGINT" property="channelGameId" />
    <result column="channel_prop_id" jdbcType="BIGINT" property="channelPropId" />
    <result column="app_id" jdbcType="BIGINT" property="appId" />
    <result column="channel_id" jdbcType="BIGINT" property="channelId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="prop_desc" jdbcType="VARCHAR" property="propDesc" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="duration_sec" jdbcType="INTEGER" property="durationSec" />
    <result column="timeliness" jdbcType="BIT" property="timeliness" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, channel_game_id, channel_prop_id, app_id, channel_id, name, prop_desc, type, 
    duration_sec, timeliness, icon_url, remark, deleted, operator, create_time, modify_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from game_prop
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByChannelGameIdAndChannelPropId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from game_prop
    where channel_game_id = #{channelGameId,jdbcType=BIGINT}
      and channel_prop_id = #{channelPropId,jdbcType=BIGINT}
      and deleted = 0
  </select>

  <select id="selectByConditions" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from game_prop
    where deleted = 0
    <if test="channelGameId != null">
      and channel_game_id = #{channelGameId,jdbcType=BIGINT}
    </if>
    <if test="channelId != null">
      and channel_id = #{channelId,jdbcType=BIGINT}
    </if>
    <if test="type != null">
      and type = #{type,jdbcType=INTEGER}
    </if>
    order by create_time desc
    <if test="offset != null and limit != null">
      limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </if>
  </select>

  <select id="countByConditions" resultType="java.lang.Integer">
    select count(*)
    from game_prop
    where deleted = 0
    <if test="channelGameId != null">
      and channel_game_id = #{channelGameId,jdbcType=BIGINT}
    </if>
    <if test="channelId != null">
      and channel_id = #{channelId,jdbcType=BIGINT}
    </if>
    <if test="type != null">
      and type = #{type,jdbcType=INTEGER}
    </if>
  </select>

  <insert id="insert" parameterType="fm.lizhi.ocean.seal.dao.bean.GamePropBean">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into game_prop (channel_game_id, channel_prop_id, app_id, 
      channel_id, name, prop_desc, 
      type, duration_sec, timeliness, 
      icon_url, remark, deleted, 
      operator, create_time, modify_time)
    values (#{channelGameId,jdbcType=BIGINT}, #{channelPropId,jdbcType=BIGINT}, #{appId,jdbcType=BIGINT}, 
      #{channelId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{propDesc,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{durationSec,jdbcType=INTEGER}, #{timeliness,jdbcType=BIT}, 
      #{iconUrl,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{deleted,jdbcType=BIT}, 
      #{operator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="insertSelective" parameterType="fm.lizhi.ocean.seal.dao.bean.GamePropBean">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into game_prop
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="channelGameId != null">
        channel_game_id,
      </if>
      <if test="channelPropId != null">
        channel_prop_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="propDesc != null">
        prop_desc,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="durationSec != null">
        duration_sec,
      </if>
      <if test="timeliness != null">
        timeliness,
      </if>
      <if test="iconUrl != null">
        icon_url,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="channelGameId != null">
        #{channelGameId,jdbcType=BIGINT},
      </if>
      <if test="channelPropId != null">
        #{channelPropId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="propDesc != null">
        #{propDesc,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="durationSec != null">
        #{durationSec,jdbcType=INTEGER},
      </if>
      <if test="timeliness != null">
        #{timeliness,jdbcType=BIT},
      </if>
      <if test="iconUrl != null">
        #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="fm.lizhi.ocean.seal.dao.bean.GamePropBean">
    update game_prop
    <set>
      <if test="channelGameId != null">
        channel_game_id = #{channelGameId,jdbcType=BIGINT},
      </if>
      <if test="channelPropId != null">
        channel_prop_id = #{channelPropId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=BIGINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="propDesc != null">
        prop_desc = #{propDesc,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="durationSec != null">
        duration_sec = #{durationSec,jdbcType=INTEGER},
      </if>
      <if test="timeliness != null">
        timeliness = #{timeliness,jdbcType=BIT},
      </if>
      <if test="iconUrl != null">
        icon_url = #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="fm.lizhi.ocean.seal.dao.bean.GamePropBean">
    update game_prop
    set channel_game_id = #{channelGameId,jdbcType=BIGINT},
      channel_prop_id = #{channelPropId,jdbcType=BIGINT},
      app_id = #{appId,jdbcType=BIGINT},
      channel_id = #{channelId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      prop_desc = #{propDesc,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      duration_sec = #{durationSec,jdbcType=INTEGER},
      timeliness = #{timeliness,jdbcType=BIT},
      icon_url = #{iconUrl,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      operator = #{operator,jdbcType=VARCHAR},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="deleteByPrimaryKey">
    update game_prop
    set deleted = 1,
        operator = #{operator,jdbcType=VARCHAR},
        modify_time = now()
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>
