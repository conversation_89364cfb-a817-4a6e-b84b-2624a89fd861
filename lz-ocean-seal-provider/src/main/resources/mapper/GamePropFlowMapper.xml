<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="fm.lizhi.ocean.seal.dao.mapper.GamePropFlowMapper">
  
  <resultMap id="BaseResultMap" type="fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="prop_id" jdbcType="BIGINT" property="propId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="unique_id" jdbcType="VARCHAR" property="uniqueId" />
    <result column="duration_sec" jdbcType="INTEGER" property="durationSec" />
    <result column="channel_game_id" jdbcType="BIGINT" property="channelGameId" />
    <result column="channel_prop_id" jdbcType="BIGINT" property="channelPropId" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="app_id" jdbcType="BIGINT" property="appId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="grant_status" jdbcType="INTEGER" property="grantStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, prop_id, user_id, unique_id, duration_sec, channel_game_id, channel_prop_id, 
    num, app_id, type, grant_status, create_time, modify_time
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from game_prop_flow
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByUniqueId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from game_prop_flow
    where unique_id = #{uniqueId,jdbcType=VARCHAR}
  </select>

  <select id="selectByConditions" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from game_prop_flow
    where 1=1
    <if test="userId != null">
      and user_id = #{userId,jdbcType=BIGINT}
    </if>
    <if test="propId != null">
      and prop_id = #{propId,jdbcType=BIGINT}
    </if>
    <if test="grantStatus != null">
      and grant_status = #{grantStatus,jdbcType=INTEGER}
    </if>
    <if test="startTime != null">
      and create_time >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
    </if>
    order by create_time desc
    <if test="offset != null and limit != null">
      limit #{offset,jdbcType=INTEGER}, #{limit,jdbcType=INTEGER}
    </if>
  </select>

  <select id="countByConditions" resultType="java.lang.Integer">
    select count(*)
    from game_prop_flow
    where 1=1
    <if test="userId != null">
      and user_id = #{userId,jdbcType=BIGINT}
    </if>
    <if test="propId != null">
      and prop_id = #{propId,jdbcType=BIGINT}
    </if>
    <if test="grantStatus != null">
      and grant_status = #{grantStatus,jdbcType=INTEGER}
    </if>
    <if test="startTime != null">
      and create_time >= #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null">
      and create_time &lt;= #{endTime,jdbcType=TIMESTAMP}
    </if>
  </select>

  <select id="selectByUserIdAndPropId" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from game_prop_flow
    where user_id = #{userId,jdbcType=BIGINT}
      and prop_id = #{propId,jdbcType=BIGINT}
    <if test="grantStatus != null">
      and grant_status = #{grantStatus,jdbcType=INTEGER}
    </if>
    order by create_time desc
  </select>

  <insert id="insert" parameterType="fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into game_prop_flow (prop_id, user_id, unique_id, 
      duration_sec, channel_game_id, channel_prop_id, 
      num, app_id, type, 
      grant_status, create_time, modify_time)
    values (#{propId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{uniqueId,jdbcType=VARCHAR}, 
      #{durationSec,jdbcType=INTEGER}, #{channelGameId,jdbcType=BIGINT}, #{channelPropId,jdbcType=BIGINT}, 
      #{num,jdbcType=INTEGER}, #{appId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, 
      #{grantStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP})
  </insert>

  <insert id="insertSelective" parameterType="fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into game_prop_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="propId != null">
        prop_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="uniqueId != null">
        unique_id,
      </if>
      <if test="durationSec != null">
        duration_sec,
      </if>
      <if test="channelGameId != null">
        channel_game_id,
      </if>
      <if test="channelPropId != null">
        channel_prop_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="grantStatus != null">
        grant_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="propId != null">
        #{propId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="uniqueId != null">
        #{uniqueId,jdbcType=VARCHAR},
      </if>
      <if test="durationSec != null">
        #{durationSec,jdbcType=INTEGER},
      </if>
      <if test="channelGameId != null">
        #{channelGameId,jdbcType=BIGINT},
      </if>
      <if test="channelPropId != null">
        #{channelPropId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="grantStatus != null">
        #{grantStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean">
    update game_prop_flow
    <set>
      <if test="propId != null">
        prop_id = #{propId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="uniqueId != null">
        unique_id = #{uniqueId,jdbcType=VARCHAR},
      </if>
      <if test="durationSec != null">
        duration_sec = #{durationSec,jdbcType=INTEGER},
      </if>
      <if test="channelGameId != null">
        channel_game_id = #{channelGameId,jdbcType=BIGINT},
      </if>
      <if test="channelPropId != null">
        channel_prop_id = #{channelPropId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=INTEGER},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="grantStatus != null">
        grant_status = #{grantStatus,jdbcType=INTEGER},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="fm.lizhi.ocean.seal.dao.bean.GamePropFlowBean">
    update game_prop_flow
    set prop_id = #{propId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      unique_id = #{uniqueId,jdbcType=VARCHAR},
      duration_sec = #{durationSec,jdbcType=INTEGER},
      channel_game_id = #{channelGameId,jdbcType=BIGINT},
      channel_prop_id = #{channelPropId,jdbcType=BIGINT},
      num = #{num,jdbcType=INTEGER},
      app_id = #{appId,jdbcType=BIGINT},
      type = #{type,jdbcType=INTEGER},
      grant_status = #{grantStatus,jdbcType=INTEGER},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateGrantStatus">
    update game_prop_flow 
    set grant_status = #{grantStatus,jdbcType=INTEGER},
        modify_time = now()
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>
