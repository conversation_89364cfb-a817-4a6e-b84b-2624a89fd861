package fm.lizhi.ocean.seal.constant;

/**
 * 游戏道具事件类型
 * <AUTHOR>
 */
public enum GamePropEventType {

    /**
     * 道具发放
     */
    GRANT_PROP("grantProp", "道具发放"),

    /**
     * 道具回收
     */
    REVOKE_PROP("revokeProp", "道具回收"),

    /**
     * 道具使用
     */
    USE_PROP("useProp", "道具使用"),

    /**
     * 道具过期
     */
    EXPIRE_PROP("expireProp", "道具过期");

    private final String event;
    private final String description;

    GamePropEventType(String event, String description) {
        this.event = event;
        this.description = description;
    }

    public String getEvent() {
        return event;
    }

    public String getDescription() {
        return description;
    }

    public static GamePropEventType from(String event) {
        for (GamePropEventType value : values()) {
            if (value.getEvent().equals(event)) {
                return value;
            }
        }
        return null;
    }
}
