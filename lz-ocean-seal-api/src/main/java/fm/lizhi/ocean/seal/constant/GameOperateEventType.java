package fm.lizhi.ocean.seal.constant;

/**
 * 游戏操作事件
 * <AUTHOR>
 */
public enum GameOperateEventType {

    /**
     * 用户加入游戏
     */
    USER_IN("user_in"),

    /**
     * 用户退出游戏
     */
    USER_OUT("user_out"),

    /**
     * 游戏开始
     */
    GAME_START("game_start"),

    /**
     * 游戏结束
     */
    GAME_END("game_end"),

    /**
     * 道具发放
     */
    GRANT_PROP("grantProp"),

    ;

    private final String event;

    GameOperateEventType(String event) {
        this.event = event;
    }

    public String getEvent() {
        return event;
    }

    public static GameOperateEventType from(String event) {
        for (GameOperateEventType value : values()) {
            if (value.getEvent().equals(event)) {
                return value;
            }
        }
        return null;
    }
}

