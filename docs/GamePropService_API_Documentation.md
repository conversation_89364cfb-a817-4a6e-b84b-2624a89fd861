# 游戏道具服务 API 文档

## 概述

游戏道具服务提供了完整的道具管理功能，包括道具发放、道具列表查询、道具流水管理等功能。该服务按照时序图实现了完整的道具发放流程。

## 接口列表

### 1. 道具发放接口

**接口名称：** `grantGameProp`  
**接口编号：** domain=4302, op=250  
**功能描述：** 根据时序图实现道具发放流程

#### 请求参数

```protobuf
message GrantGamePropParam {
    optional int64 userId = 1;        // 用户ID（必填）
    optional int64 propId = 2;        // 道具ID（必填）
    optional int32 num = 3;           // 发放数量（必填）
    optional string uniqueId = 4;     // 幂等的唯一ID（必填）
    optional string appId = 5;        // 应用ID
    optional string gameId = 6;       // 游戏ID
    optional string channel = 7;      // 渠道
    optional string remark = 8;       // 备注
}
```

#### 响应参数

```protobuf
message ResponseGrantGameProp {
    optional int32 grantStatus = 1;   // 发放状态：0未发放，1发放中，2成功，3失败
    optional string message = 2;      // 消息
    optional int64 flowId = 3;        // 流水ID
}
```

#### 返回码

- 0: 发放成功
- 1: 参数错误
- 2: 道具不存在
- 3: 用户不存在
- 4: 发放失败
- 5: 内部错误

#### 时序图流程

1. 业务服务端调用 Seal 服务端道具发放接口
2. Seal 查询道具是否已配置（查询 `game_prop` 表）
3. 调用游戏厂商服务端发放接口（使用 GameProxyManager 策略模式）
4. 记录发放流水到 `game_prop_flow` 表
5. 返回发放状态给业务服务端

### 2. 道具列表接口

**接口名称：** `getGamePropList`  
**接口编号：** domain=4302, op=251  
**功能描述：** 获取道具列表

#### 请求参数

```protobuf
message GetGamePropListParam {
    optional int64 channelGameId = 1; // 渠道游戏ID
    optional int64 channelId = 2;     // 渠道ID
    optional int32 type = 3;          // 道具类型：1皮肤，2道具
    optional int32 pageNumber = 4;    // 页码
    optional int32 pageSize = 5;      // 页大小
}
```

#### 响应参数

```protobuf
message ResponseGetGamePropList {
    repeated GameProp gameProps = 1;  // 道具列表
    optional int32 total = 2;         // 总数
    optional int32 pageNumber = 3;    // 页码
    optional int32 pageSize = 4;      // 页大小
}
```

### 3. 道具流水查询接口

**接口名称：** `getGamePropFlowList`  
**接口编号：** domain=4302, op=252  
**功能描述：** 查询道具流水列表

#### 请求参数

```protobuf
message GetGamePropFlowListParam {
    optional int64 userId = 1;        // 用户ID
    optional int64 propId = 2;        // 道具ID
    optional int32 grantStatus = 3;   // 发放状态
    optional int64 startTime = 4;     // 开始时间（时间戳）
    optional int64 endTime = 5;       // 结束时间（时间戳）
    optional int32 pageNumber = 6;    // 页码
    optional int32 pageSize = 7;      // 页大小
}
```

### 4. 道具流水写入接口

**接口名称：** `addGamePropFlow`  
**接口编号：** domain=4302, op=253  
**功能描述：** 写入道具流水记录

## 数据库表结构

### game_prop 表（道具表）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| channel_game_id | BIGINT | 渠道游戏ID |
| channel_prop_id | BIGINT | 渠道道具ID |
| app_id | BIGINT | Seal分发给业务的ID |
| channel_id | BIGINT | 渠道ID |
| name | VARCHAR | 道具名称 |
| prop_desc | VARCHAR | 道具描述 |
| type | INT | 道具类型：1皮肤，2道具 |
| duration_sec | INT | 有效时长（秒），<0为永久 |
| timeliness | BOOLEAN | 是否时效性道具 |
| icon_url | VARCHAR | 参考图片 |
| remark | VARCHAR | 备注 |
| deleted | BOOLEAN | 是否删除：0未删除，1已删除 |
| operator | VARCHAR | 操作人 |
| create_time | TIMESTAMP | 创建时间 |
| modify_time | TIMESTAMP | 更新时间 |

### game_prop_flow 表（道具流水表）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| prop_id | BIGINT | 道具ID |
| user_id | BIGINT | 用户ID |
| unique_id | VARCHAR | 幂等的唯一ID |
| duration_sec | INT | 有效时长（秒） |
| channel_game_id | BIGINT | 渠道游戏ID |
| channel_prop_id | BIGINT | 渠道道具ID |
| num | INT | 发放数量 |
| app_id | BIGINT | Seal分发给业务的ID |
| type | INT | 道具类型：1皮肤，2道具 |
| grant_status | INT | 发放状态：0未发放，1发放中，2成功，3失败 |
| create_time | TIMESTAMP | 创建时间 |
| modify_time | TIMESTAMP | 更新时间 |

## 使用示例

### Java 客户端调用示例

```java
// 1. 道具发放
GrantGamePropParam grantParam = GrantGamePropParam.newBuilder()
    .setUserId(12345L)
    .setPropId(1L)
    .setNum(1)
    .setUniqueId("unique-id-" + System.currentTimeMillis())
    .setAppId("your-app-id")
    .setGameId("your-game-id")
    .setChannel("sud")
    .setRemark("测试发放")
    .build();

Result<ResponseGrantGameProp> grantResult = gamePropService.grantGameProp(grantParam);
if (grantResult.getRcode() == 0) {
    System.out.println("道具发放成功，流水ID：" + grantResult.getResult().getFlowId());
}

// 2. 查询道具列表
GetGamePropListParam listParam = GetGamePropListParam.newBuilder()
    .setChannelGameId(1L)
    .setType(1) // 皮肤类型
    .setPageNumber(1)
    .setPageSize(10)
    .build();

Result<ResponseGetGamePropList> listResult = gamePropService.getGamePropList(listParam);
if (listResult.getRcode() == 0) {
    System.out.println("查询到道具数量：" + listResult.getResult().getTotal());
}

// 3. 查询道具流水
GetGamePropFlowListParam flowParam = GetGamePropFlowListParam.newBuilder()
    .setUserId(12345L)
    .setGrantStatus(2) // 成功状态
    .setPageNumber(1)
    .setPageSize(10)
    .build();

Result<ResponseGetGamePropFlowList> flowResult = gamePropService.getGamePropFlowList(flowParam);
```

## 注意事项

1. **幂等性保证**：使用 `uniqueId` 字段确保道具发放的幂等性，相同的 `uniqueId` 只会发放一次
2. **事务管理**：道具发放和流水记录使用事务保证数据一致性
3. **策略模式**：支持不同渠道（SUD、LUK）的道具发放，通过策略模式实现
4. **错误处理**：完善的异常处理和状态管理，确保系统稳定性
5. **分页查询**：列表查询接口支持分页，避免大数据量查询问题

## 扩展说明

该服务设计为可扩展架构：

1. **新增渠道支持**：通过在 `GameOperateEventMapping` 中添加新的事件映射即可支持新渠道
2. **新增道具类型**：通过扩展 `type` 字段的枚举值支持新的道具类型
3. **新增业务逻辑**：通过扩展 `GamePropManager` 添加新的业务逻辑
